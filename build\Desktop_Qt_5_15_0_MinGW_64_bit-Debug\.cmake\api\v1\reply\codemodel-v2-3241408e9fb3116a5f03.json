{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.17"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "S4", "targetIndexes": [0, 1, 2]}], "targets": [{"directoryIndex": 0, "id": "TempDetectQt::@6890427a1f51a3e7e1df", "jsonFile": "target-TempDetectQt-Debug-30648ba5e7afefaf07c6.json", "name": "TempDetectQt", "projectIndex": 0}, {"directoryIndex": 0, "id": "TempDetectQt_autogen::@6890427a1f51a3e7e1df", "jsonFile": "target-TempDetectQt_autogen-Debug-a1127ad4ff7a51a7d738.json", "name": "TempDetectQt_autogen", "projectIndex": 0}, {"directoryIndex": 0, "id": "TempDetectQt_autogen_timestamp_deps::@6890427a1f51a3e7e1df", "jsonFile": "target-TempDetectQt_autogen_timestamp_deps-Debug-8194940c5e546a502097.json", "name": "TempDetectQt_autogen_timestamp_deps", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "C:/Users/<USER>/Desktop/TempDetectQt/build/Desktop_Qt_5_15_0_MinGW_64_bit-Debug", "source": "C:/Users/<USER>/Desktop/TempDetectQt"}, "version": {"major": 2, "minor": 7}}