# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.30

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: S4
# Configurations: Debug
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = Debug
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = C$:/Users/<USER>/Desktop/TempDetectQt/build/Desktop_Qt_5_15_0_MinGW_64_bit-Debug/
# =============================================================================
# Object build statements for EXECUTABLE target TempDetectQt


#############################################
# Order-only phony target for TempDetectQt

build cmake_object_order_depends_target_TempDetectQt: phony || TempDetectQt_autogen TempDetectQt_autogen/mocs_compilation.cpp TempDetectQt_autogen/timestamp TempDetectQt_autogen_timestamp_deps ui_mainwindow.h

build CMakeFiles/TempDetectQt.dir/TempDetectQt_autogen/mocs_compilation.cpp.obj: CXX_COMPILER__TempDetectQt_unscanned_Debug C$:/Users/<USER>/Desktop/TempDetectQt/build/Desktop_Qt_5_15_0_MinGW_64_bit-Debug/TempDetectQt_autogen/mocs_compilation.cpp || cmake_object_order_depends_target_TempDetectQt
  DEFINES = -DQT_CORE_LIB -DQT_GUI_LIB -DQT_OPENGL_LIB -DQT_PRINTSUPPORT_LIB -DQT_SERIALBUS_LIB -DQT_SERIALPORT_LIB -DQT_WIDGETS_LIB
  DEP_FILE = CMakeFiles\TempDetectQt.dir\TempDetectQt_autogen\mocs_compilation.cpp.obj.d
  FLAGS = -DQT_QML_DEBUG -g -std=gnu++17 -fdiagnostics-color=always
  INCLUDES = -IC:/Users/<USER>/Desktop/TempDetectQt/build/Desktop_Qt_5_15_0_MinGW_64_bit-Debug/TempDetectQt_autogen/include -IC:/Users/<USER>/Desktop/TempDetectQt -I/opt/homebrew/Cellar/qt@5/5.15.16/include -I/opt/homebrew/Cellar/qt@5/5.15.16/include/QtSerialBus -I/opt/homebrew/Cellar/qt@5/5.15.16/include/QtSerialPort -isystem C:/Qt/5.15.0/msvc2019_64/include -isystem C:/Qt/5.15.0/msvc2019_64/include/QtCore -isystem C:/Qt/5.15.0/msvc2019_64/./mkspecs/win32-msvc -isystem C:/Qt/5.15.0/msvc2019_64/include/QtGui -isystem C:/Qt/5.15.0/msvc2019_64/include/QtANGLE -isystem C:/Qt/5.15.0/msvc2019_64/include/QtWidgets -isystem C:/Qt/5.15.0/msvc2019_64/include/QtPrintSupport -isystem C:/Qt/5.15.0/msvc2019_64/include/QtOpenGL -isystem C:/Qt/5.15.0/msvc2019_64/include/QtSerialPort -isystem C:/Qt/5.15.0/msvc2019_64/include/QtSerialBus
  OBJECT_DIR = CMakeFiles\TempDetectQt.dir
  OBJECT_FILE_DIR = CMakeFiles\TempDetectQt.dir\TempDetectQt_autogen

build CMakeFiles/TempDetectQt.dir/main.cpp.obj: CXX_COMPILER__TempDetectQt_unscanned_Debug C$:/Users/<USER>/Desktop/TempDetectQt/main.cpp || cmake_object_order_depends_target_TempDetectQt
  DEFINES = -DQT_CORE_LIB -DQT_GUI_LIB -DQT_OPENGL_LIB -DQT_PRINTSUPPORT_LIB -DQT_SERIALBUS_LIB -DQT_SERIALPORT_LIB -DQT_WIDGETS_LIB
  DEP_FILE = CMakeFiles\TempDetectQt.dir\main.cpp.obj.d
  FLAGS = -DQT_QML_DEBUG -g -std=gnu++17 -fdiagnostics-color=always
  INCLUDES = -IC:/Users/<USER>/Desktop/TempDetectQt/build/Desktop_Qt_5_15_0_MinGW_64_bit-Debug/TempDetectQt_autogen/include -IC:/Users/<USER>/Desktop/TempDetectQt -I/opt/homebrew/Cellar/qt@5/5.15.16/include -I/opt/homebrew/Cellar/qt@5/5.15.16/include/QtSerialBus -I/opt/homebrew/Cellar/qt@5/5.15.16/include/QtSerialPort -isystem C:/Qt/5.15.0/msvc2019_64/include -isystem C:/Qt/5.15.0/msvc2019_64/include/QtCore -isystem C:/Qt/5.15.0/msvc2019_64/./mkspecs/win32-msvc -isystem C:/Qt/5.15.0/msvc2019_64/include/QtGui -isystem C:/Qt/5.15.0/msvc2019_64/include/QtANGLE -isystem C:/Qt/5.15.0/msvc2019_64/include/QtWidgets -isystem C:/Qt/5.15.0/msvc2019_64/include/QtPrintSupport -isystem C:/Qt/5.15.0/msvc2019_64/include/QtOpenGL -isystem C:/Qt/5.15.0/msvc2019_64/include/QtSerialPort -isystem C:/Qt/5.15.0/msvc2019_64/include/QtSerialBus
  OBJECT_DIR = CMakeFiles\TempDetectQt.dir
  OBJECT_FILE_DIR = CMakeFiles\TempDetectQt.dir

build CMakeFiles/TempDetectQt.dir/mainwindow.cpp.obj: CXX_COMPILER__TempDetectQt_unscanned_Debug C$:/Users/<USER>/Desktop/TempDetectQt/mainwindow.cpp || cmake_object_order_depends_target_TempDetectQt
  DEFINES = -DQT_CORE_LIB -DQT_GUI_LIB -DQT_OPENGL_LIB -DQT_PRINTSUPPORT_LIB -DQT_SERIALBUS_LIB -DQT_SERIALPORT_LIB -DQT_WIDGETS_LIB
  DEP_FILE = CMakeFiles\TempDetectQt.dir\mainwindow.cpp.obj.d
  FLAGS = -DQT_QML_DEBUG -g -std=gnu++17 -fdiagnostics-color=always
  INCLUDES = -IC:/Users/<USER>/Desktop/TempDetectQt/build/Desktop_Qt_5_15_0_MinGW_64_bit-Debug/TempDetectQt_autogen/include -IC:/Users/<USER>/Desktop/TempDetectQt -I/opt/homebrew/Cellar/qt@5/5.15.16/include -I/opt/homebrew/Cellar/qt@5/5.15.16/include/QtSerialBus -I/opt/homebrew/Cellar/qt@5/5.15.16/include/QtSerialPort -isystem C:/Qt/5.15.0/msvc2019_64/include -isystem C:/Qt/5.15.0/msvc2019_64/include/QtCore -isystem C:/Qt/5.15.0/msvc2019_64/./mkspecs/win32-msvc -isystem C:/Qt/5.15.0/msvc2019_64/include/QtGui -isystem C:/Qt/5.15.0/msvc2019_64/include/QtANGLE -isystem C:/Qt/5.15.0/msvc2019_64/include/QtWidgets -isystem C:/Qt/5.15.0/msvc2019_64/include/QtPrintSupport -isystem C:/Qt/5.15.0/msvc2019_64/include/QtOpenGL -isystem C:/Qt/5.15.0/msvc2019_64/include/QtSerialPort -isystem C:/Qt/5.15.0/msvc2019_64/include/QtSerialBus
  OBJECT_DIR = CMakeFiles\TempDetectQt.dir
  OBJECT_FILE_DIR = CMakeFiles\TempDetectQt.dir

build CMakeFiles/TempDetectQt.dir/modbusmanager.cpp.obj: CXX_COMPILER__TempDetectQt_unscanned_Debug C$:/Users/<USER>/Desktop/TempDetectQt/modbusmanager.cpp || cmake_object_order_depends_target_TempDetectQt
  DEFINES = -DQT_CORE_LIB -DQT_GUI_LIB -DQT_OPENGL_LIB -DQT_PRINTSUPPORT_LIB -DQT_SERIALBUS_LIB -DQT_SERIALPORT_LIB -DQT_WIDGETS_LIB
  DEP_FILE = CMakeFiles\TempDetectQt.dir\modbusmanager.cpp.obj.d
  FLAGS = -DQT_QML_DEBUG -g -std=gnu++17 -fdiagnostics-color=always
  INCLUDES = -IC:/Users/<USER>/Desktop/TempDetectQt/build/Desktop_Qt_5_15_0_MinGW_64_bit-Debug/TempDetectQt_autogen/include -IC:/Users/<USER>/Desktop/TempDetectQt -I/opt/homebrew/Cellar/qt@5/5.15.16/include -I/opt/homebrew/Cellar/qt@5/5.15.16/include/QtSerialBus -I/opt/homebrew/Cellar/qt@5/5.15.16/include/QtSerialPort -isystem C:/Qt/5.15.0/msvc2019_64/include -isystem C:/Qt/5.15.0/msvc2019_64/include/QtCore -isystem C:/Qt/5.15.0/msvc2019_64/./mkspecs/win32-msvc -isystem C:/Qt/5.15.0/msvc2019_64/include/QtGui -isystem C:/Qt/5.15.0/msvc2019_64/include/QtANGLE -isystem C:/Qt/5.15.0/msvc2019_64/include/QtWidgets -isystem C:/Qt/5.15.0/msvc2019_64/include/QtPrintSupport -isystem C:/Qt/5.15.0/msvc2019_64/include/QtOpenGL -isystem C:/Qt/5.15.0/msvc2019_64/include/QtSerialPort -isystem C:/Qt/5.15.0/msvc2019_64/include/QtSerialBus
  OBJECT_DIR = CMakeFiles\TempDetectQt.dir
  OBJECT_FILE_DIR = CMakeFiles\TempDetectQt.dir

build CMakeFiles/TempDetectQt.dir/qcustomplot.cpp.obj: CXX_COMPILER__TempDetectQt_unscanned_Debug C$:/Users/<USER>/Desktop/TempDetectQt/qcustomplot.cpp || cmake_object_order_depends_target_TempDetectQt
  DEFINES = -DQT_CORE_LIB -DQT_GUI_LIB -DQT_OPENGL_LIB -DQT_PRINTSUPPORT_LIB -DQT_SERIALBUS_LIB -DQT_SERIALPORT_LIB -DQT_WIDGETS_LIB
  DEP_FILE = CMakeFiles\TempDetectQt.dir\qcustomplot.cpp.obj.d
  FLAGS = -DQT_QML_DEBUG -g -std=gnu++17 -fdiagnostics-color=always
  INCLUDES = -IC:/Users/<USER>/Desktop/TempDetectQt/build/Desktop_Qt_5_15_0_MinGW_64_bit-Debug/TempDetectQt_autogen/include -IC:/Users/<USER>/Desktop/TempDetectQt -I/opt/homebrew/Cellar/qt@5/5.15.16/include -I/opt/homebrew/Cellar/qt@5/5.15.16/include/QtSerialBus -I/opt/homebrew/Cellar/qt@5/5.15.16/include/QtSerialPort -isystem C:/Qt/5.15.0/msvc2019_64/include -isystem C:/Qt/5.15.0/msvc2019_64/include/QtCore -isystem C:/Qt/5.15.0/msvc2019_64/./mkspecs/win32-msvc -isystem C:/Qt/5.15.0/msvc2019_64/include/QtGui -isystem C:/Qt/5.15.0/msvc2019_64/include/QtANGLE -isystem C:/Qt/5.15.0/msvc2019_64/include/QtWidgets -isystem C:/Qt/5.15.0/msvc2019_64/include/QtPrintSupport -isystem C:/Qt/5.15.0/msvc2019_64/include/QtOpenGL -isystem C:/Qt/5.15.0/msvc2019_64/include/QtSerialPort -isystem C:/Qt/5.15.0/msvc2019_64/include/QtSerialBus
  OBJECT_DIR = CMakeFiles\TempDetectQt.dir
  OBJECT_FILE_DIR = CMakeFiles\TempDetectQt.dir

build CMakeFiles/TempDetectQt.dir/temperaturedata.cpp.obj: CXX_COMPILER__TempDetectQt_unscanned_Debug C$:/Users/<USER>/Desktop/TempDetectQt/temperaturedata.cpp || cmake_object_order_depends_target_TempDetectQt
  DEFINES = -DQT_CORE_LIB -DQT_GUI_LIB -DQT_OPENGL_LIB -DQT_PRINTSUPPORT_LIB -DQT_SERIALBUS_LIB -DQT_SERIALPORT_LIB -DQT_WIDGETS_LIB
  DEP_FILE = CMakeFiles\TempDetectQt.dir\temperaturedata.cpp.obj.d
  FLAGS = -DQT_QML_DEBUG -g -std=gnu++17 -fdiagnostics-color=always
  INCLUDES = -IC:/Users/<USER>/Desktop/TempDetectQt/build/Desktop_Qt_5_15_0_MinGW_64_bit-Debug/TempDetectQt_autogen/include -IC:/Users/<USER>/Desktop/TempDetectQt -I/opt/homebrew/Cellar/qt@5/5.15.16/include -I/opt/homebrew/Cellar/qt@5/5.15.16/include/QtSerialBus -I/opt/homebrew/Cellar/qt@5/5.15.16/include/QtSerialPort -isystem C:/Qt/5.15.0/msvc2019_64/include -isystem C:/Qt/5.15.0/msvc2019_64/include/QtCore -isystem C:/Qt/5.15.0/msvc2019_64/./mkspecs/win32-msvc -isystem C:/Qt/5.15.0/msvc2019_64/include/QtGui -isystem C:/Qt/5.15.0/msvc2019_64/include/QtANGLE -isystem C:/Qt/5.15.0/msvc2019_64/include/QtWidgets -isystem C:/Qt/5.15.0/msvc2019_64/include/QtPrintSupport -isystem C:/Qt/5.15.0/msvc2019_64/include/QtOpenGL -isystem C:/Qt/5.15.0/msvc2019_64/include/QtSerialPort -isystem C:/Qt/5.15.0/msvc2019_64/include/QtSerialBus
  OBJECT_DIR = CMakeFiles\TempDetectQt.dir
  OBJECT_FILE_DIR = CMakeFiles\TempDetectQt.dir


# =============================================================================
# Link build statements for EXECUTABLE target TempDetectQt


#############################################
# Link the executable TempDetectQt.exe

build TempDetectQt.exe: CXX_EXECUTABLE_LINKER__TempDetectQt_Debug CMakeFiles/TempDetectQt.dir/TempDetectQt_autogen/mocs_compilation.cpp.obj CMakeFiles/TempDetectQt.dir/main.cpp.obj CMakeFiles/TempDetectQt.dir/mainwindow.cpp.obj CMakeFiles/TempDetectQt.dir/modbusmanager.cpp.obj CMakeFiles/TempDetectQt.dir/qcustomplot.cpp.obj CMakeFiles/TempDetectQt.dir/temperaturedata.cpp.obj | C$:/Qt/5.15.0/msvc2019_64/lib/Qt5PrintSupportd.lib C$:/Qt/5.15.0/msvc2019_64/lib/Qt5OpenGLd.lib C$:/Qt/5.15.0/msvc2019_64/lib/Qt5SerialPortd.lib C$:/Qt/5.15.0/msvc2019_64/lib/Qt5SerialBusd.lib C$:/Qt/5.15.0/msvc2019_64/lib/Qt5Widgetsd.lib C$:/Qt/5.15.0/msvc2019_64/lib/Qt5Guid.lib C$:/Qt/5.15.0/msvc2019_64/lib/Qt5Cored.lib || TempDetectQt_autogen TempDetectQt_autogen_timestamp_deps
  FLAGS = -DQT_QML_DEBUG -g
  LINK_LIBRARIES = C:/Qt/5.15.0/msvc2019_64/lib/Qt5PrintSupportd.lib  C:/Qt/5.15.0/msvc2019_64/lib/Qt5OpenGLd.lib  C:/Qt/5.15.0/msvc2019_64/lib/Qt5SerialPortd.lib  C:/Qt/5.15.0/msvc2019_64/lib/Qt5SerialBusd.lib  C:/Qt/5.15.0/msvc2019_64/lib/Qt5Widgetsd.lib  C:/Qt/5.15.0/msvc2019_64/lib/Qt5Guid.lib  C:/Qt/5.15.0/msvc2019_64/lib/Qt5Cored.lib  -lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32
  OBJECT_DIR = CMakeFiles\TempDetectQt.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  TARGET_FILE = TempDetectQt.exe
  TARGET_IMPLIB = libTempDetectQt.dll.a
  TARGET_PDB = TempDetectQt.exe.dbg


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\Desktop\TempDetectQt\build\Desktop_Qt_5_15_0_MinGW_64_bit-Debug && C:\Qt\Tools\CMake_64\bin\cmake-gui.exe -SC:\Users\<USER>\Desktop\TempDetectQt -BC:\Users\<USER>\Desktop\TempDetectQt\build\Desktop_Qt_5_15_0_MinGW_64_bit-Debug"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\Desktop\TempDetectQt\build\Desktop_Qt_5_15_0_MinGW_64_bit-Debug && C:\Qt\Tools\CMake_64\bin\cmake.exe --regenerate-during-build -SC:\Users\<USER>\Desktop\TempDetectQt -BC:\Users\<USER>\Desktop\TempDetectQt\build\Desktop_Qt_5_15_0_MinGW_64_bit-Debug"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util


#############################################
# Utility command for TempDetectQt_autogen_timestamp_deps

build TempDetectQt_autogen_timestamp_deps: phony


#############################################
# Utility command for TempDetectQt_autogen

build TempDetectQt_autogen: phony CMakeFiles/TempDetectQt_autogen TempDetectQt_autogen/timestamp TempDetectQt_autogen/mocs_compilation.cpp TempDetectQt_autogen_timestamp_deps


#############################################
# Custom command for ui_mainwindow.h

build ui_mainwindow.h | ${cmake_ninja_workdir}ui_mainwindow.h: CUSTOM_COMMAND C$:/Users/<USER>/Desktop/TempDetectQt/mainwindow.ui || TempDetectQt_autogen TempDetectQt_autogen_timestamp_deps
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\Desktop\TempDetectQt\build\Desktop_Qt_5_15_0_MinGW_64_bit-Debug && C:\Qt\5.15.0\msvc2019_64\bin\uic.exe -o C:/Users/<USER>/Desktop/TempDetectQt/build/Desktop_Qt_5_15_0_MinGW_64_bit-Debug/ui_mainwindow.h C:/Users/<USER>/Desktop/TempDetectQt/mainwindow.ui"
  DESC = Generating ui_mainwindow.h
  restat = 1


#############################################
# Custom command for TempDetectQt_autogen\timestamp

build TempDetectQt_autogen/timestamp TempDetectQt_autogen/mocs_compilation.cpp | ${cmake_ninja_workdir}TempDetectQt_autogen/timestamp ${cmake_ninja_workdir}TempDetectQt_autogen/mocs_compilation.cpp: CUSTOM_COMMAND C$:/Qt/5.15.0/msvc2019_64/bin/moc.exe C$:/Qt/5.15.0/msvc2019_64/bin/uic.exe || TempDetectQt_autogen_timestamp_deps
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\Desktop\TempDetectQt\build\Desktop_Qt_5_15_0_MinGW_64_bit-Debug && C:\Qt\Tools\CMake_64\bin\cmake.exe -E cmake_autogen C:/Users/<USER>/Desktop/TempDetectQt/build/Desktop_Qt_5_15_0_MinGW_64_bit-Debug/CMakeFiles/TempDetectQt_autogen.dir/AutogenInfo.json Debug && C:\Qt\Tools\CMake_64\bin\cmake.exe -E touch C:/Users/<USER>/Desktop/TempDetectQt/build/Desktop_Qt_5_15_0_MinGW_64_bit-Debug/TempDetectQt_autogen/timestamp && C:\Qt\Tools\CMake_64\bin\cmake.exe -E cmake_transform_depfile Ninja gccdepfile C:/Users/<USER>/Desktop/TempDetectQt C:/Users/<USER>/Desktop/TempDetectQt C:/Users/<USER>/Desktop/TempDetectQt/build/Desktop_Qt_5_15_0_MinGW_64_bit-Debug C:/Users/<USER>/Desktop/TempDetectQt/build/Desktop_Qt_5_15_0_MinGW_64_bit-Debug C:/Users/<USER>/Desktop/TempDetectQt/build/Desktop_Qt_5_15_0_MinGW_64_bit-Debug/TempDetectQt_autogen/deps C:/Users/<USER>/Desktop/TempDetectQt/build/Desktop_Qt_5_15_0_MinGW_64_bit-Debug/CMakeFiles/d/724609b43729b8b061f920b6750d178816df29aba7dd5dd939749efc4c3152e0.d"
  DESC = Automatic MOC and UIC for target TempDetectQt
  depfile = CMakeFiles\d\724609b43729b8b061f920b6750d178816df29aba7dd5dd939749efc4c3152e0.d
  deps = gcc
  restat = 1


#############################################
# Phony custom command for CMakeFiles\TempDetectQt_autogen

build CMakeFiles/TempDetectQt_autogen | ${cmake_ninja_workdir}CMakeFiles/TempDetectQt_autogen: phony TempDetectQt_autogen/timestamp || TempDetectQt_autogen_timestamp_deps

# =============================================================================
# Target aliases.

build TempDetectQt: phony TempDetectQt.exe

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/Desktop/TempDetectQt/build/Desktop_Qt_5_15_0_MinGW_64_bit-Debug

build all: phony TempDetectQt.exe

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE | .qtc/package-manager/auto-setup.cmake .qtc/package-manager/maintenance_tool_provider.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Core/Qt5CoreConfig.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Core/Qt5CoreConfigExtras.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Core/Qt5CoreConfigExtrasMkspecDir.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Core/Qt5CoreConfigVersion.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Core/Qt5CoreMacros.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Gui/Qt5GuiConfig.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Gui/Qt5GuiConfigExtras.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Gui/Qt5GuiConfigVersion.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Gui/Qt5Gui_QGifPlugin.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Gui/Qt5Gui_QICNSPlugin.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Gui/Qt5Gui_QICOPlugin.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Gui/Qt5Gui_QJpegPlugin.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Gui/Qt5Gui_QMinimalIntegrationPlugin.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Gui/Qt5Gui_QOffscreenIntegrationPlugin.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Gui/Qt5Gui_QSvgIconPlugin.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Gui/Qt5Gui_QSvgPlugin.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Gui/Qt5Gui_QTgaPlugin.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Gui/Qt5Gui_QTiffPlugin.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Gui/Qt5Gui_QTuioTouchPlugin.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Gui/Qt5Gui_QVirtualKeyboardPlugin.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Gui/Qt5Gui_QWbmpPlugin.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Gui/Qt5Gui_QWebGLIntegrationPlugin.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Gui/Qt5Gui_QWebpPlugin.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Gui/Qt5Gui_QWindowsDirect2DIntegrationPlugin.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Gui/Qt5Gui_QWindowsIntegrationPlugin.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Gui/Qt5Gui_QXdgDesktopPortalThemePlugin.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5OpenGL/Qt5OpenGLConfig.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5OpenGL/Qt5OpenGLConfigVersion.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5PrintSupport/Qt5PrintSupportConfig.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5PrintSupport/Qt5PrintSupportConfigVersion.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5PrintSupport/Qt5PrintSupport_QWindowsPrinterSupportPlugin.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5SerialBus/Qt5SerialBusConfig.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5SerialBus/Qt5SerialBusConfigVersion.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5SerialBus/Qt5SerialBus_PassThruCanBusPlugin.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5SerialBus/Qt5SerialBus_PeakCanBusPlugin.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5SerialBus/Qt5SerialBus_SystecCanBusPlugin.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5SerialBus/Qt5SerialBus_TinyCanBusPlugin.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5SerialBus/Qt5SerialBus_VectorCanBusPlugin.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5SerialBus/Qt5SerialBus_VirtualCanBusPlugin.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5SerialPort/Qt5SerialPortConfig.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5SerialPort/Qt5SerialPortConfigVersion.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Widgets/Qt5WidgetsConfig.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Widgets/Qt5WidgetsConfigExtras.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Widgets/Qt5WidgetsConfigVersion.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Widgets/Qt5WidgetsMacros.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Widgets/Qt5Widgets_QWindowsVistaStylePlugin.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5/Qt5Config.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5/Qt5ConfigVersion.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5/Qt5ModuleLocation.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeCInformation.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeCXXInformation.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeCommonLanguageInclude.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeGenericSystem.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeInitializeConfigs.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeLanguageInformation.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeParseArguments.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeRCInformation.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeSystemSpecificInformation.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeSystemSpecificInitialize.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/CMakeCommonCompilerMacros.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/GNU-C.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/GNU-CXX.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/GNU.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/Platform/Windows-GNU-C-ABI.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/Platform/Windows-GNU-C.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/Platform/Windows-GNU-CXX-ABI.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/Platform/Windows-GNU-CXX.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/Platform/Windows-GNU.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/Platform/Windows-Initialize.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/Platform/Windows-windres.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/Platform/Windows.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/Platform/WindowsPaths.cmake C$:/Users/<USER>/Desktop/TempDetectQt/CMakeLists.txt CMakeCache.txt CMakeFiles/3.30.5/CMakeCCompiler.cmake CMakeFiles/3.30.5/CMakeCXXCompiler.cmake CMakeFiles/3.30.5/CMakeRCCompiler.cmake CMakeFiles/3.30.5/CMakeSystem.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build .qtc/package-manager/auto-setup.cmake .qtc/package-manager/maintenance_tool_provider.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Core/Qt5CoreConfig.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Core/Qt5CoreConfigExtras.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Core/Qt5CoreConfigExtrasMkspecDir.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Core/Qt5CoreConfigVersion.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Core/Qt5CoreMacros.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Gui/Qt5GuiConfig.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Gui/Qt5GuiConfigExtras.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Gui/Qt5GuiConfigVersion.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Gui/Qt5Gui_QGifPlugin.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Gui/Qt5Gui_QICNSPlugin.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Gui/Qt5Gui_QICOPlugin.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Gui/Qt5Gui_QJpegPlugin.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Gui/Qt5Gui_QMinimalIntegrationPlugin.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Gui/Qt5Gui_QOffscreenIntegrationPlugin.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Gui/Qt5Gui_QSvgIconPlugin.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Gui/Qt5Gui_QSvgPlugin.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Gui/Qt5Gui_QTgaPlugin.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Gui/Qt5Gui_QTiffPlugin.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Gui/Qt5Gui_QTuioTouchPlugin.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Gui/Qt5Gui_QVirtualKeyboardPlugin.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Gui/Qt5Gui_QWbmpPlugin.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Gui/Qt5Gui_QWebGLIntegrationPlugin.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Gui/Qt5Gui_QWebpPlugin.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Gui/Qt5Gui_QWindowsDirect2DIntegrationPlugin.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Gui/Qt5Gui_QWindowsIntegrationPlugin.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Gui/Qt5Gui_QXdgDesktopPortalThemePlugin.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5OpenGL/Qt5OpenGLConfig.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5OpenGL/Qt5OpenGLConfigVersion.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5PrintSupport/Qt5PrintSupportConfig.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5PrintSupport/Qt5PrintSupportConfigVersion.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5PrintSupport/Qt5PrintSupport_QWindowsPrinterSupportPlugin.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5SerialBus/Qt5SerialBusConfig.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5SerialBus/Qt5SerialBusConfigVersion.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5SerialBus/Qt5SerialBus_PassThruCanBusPlugin.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5SerialBus/Qt5SerialBus_PeakCanBusPlugin.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5SerialBus/Qt5SerialBus_SystecCanBusPlugin.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5SerialBus/Qt5SerialBus_TinyCanBusPlugin.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5SerialBus/Qt5SerialBus_VectorCanBusPlugin.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5SerialBus/Qt5SerialBus_VirtualCanBusPlugin.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5SerialPort/Qt5SerialPortConfig.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5SerialPort/Qt5SerialPortConfigVersion.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Widgets/Qt5WidgetsConfig.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Widgets/Qt5WidgetsConfigExtras.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Widgets/Qt5WidgetsConfigVersion.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Widgets/Qt5WidgetsMacros.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Widgets/Qt5Widgets_QWindowsVistaStylePlugin.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5/Qt5Config.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5/Qt5ConfigVersion.cmake C$:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5/Qt5ModuleLocation.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeCInformation.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeCXXInformation.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeCommonLanguageInclude.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeGenericSystem.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeInitializeConfigs.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeLanguageInformation.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeParseArguments.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeRCInformation.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeSystemSpecificInformation.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeSystemSpecificInitialize.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/CMakeCommonCompilerMacros.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/GNU-C.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/GNU-CXX.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/GNU.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/Platform/Windows-GNU-C-ABI.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/Platform/Windows-GNU-C.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/Platform/Windows-GNU-CXX-ABI.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/Platform/Windows-GNU-CXX.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/Platform/Windows-GNU.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/Platform/Windows-Initialize.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/Platform/Windows-windres.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/Platform/Windows.cmake C$:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/Platform/WindowsPaths.cmake C$:/Users/<USER>/Desktop/TempDetectQt/CMakeLists.txt CMakeCache.txt CMakeFiles/3.30.5/CMakeCCompiler.cmake CMakeFiles/3.30.5/CMakeCXXCompiler.cmake CMakeFiles/3.30.5/CMakeRCCompiler.cmake CMakeFiles/3.30.5/CMakeSystem.cmake: phony


#############################################
# Clean additional files.

build CMakeFiles/clean.additional: CLEAN_ADDITIONAL
  CONFIG = Debug


#############################################
# Clean all the built files.

build clean: CLEAN CMakeFiles/clean.additional


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
