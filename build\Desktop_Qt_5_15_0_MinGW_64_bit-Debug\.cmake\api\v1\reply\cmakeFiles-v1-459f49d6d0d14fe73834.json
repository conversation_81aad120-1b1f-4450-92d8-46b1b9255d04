{"inputs": [{"path": "CMakeLists.txt"}, {"isGenerated": true, "path": "build/Desktop_Qt_5_15_0_MinGW_64_bit-Debug/.qtc/package-manager/auto-setup.cmake"}, {"isGenerated": true, "path": "build/Desktop_Qt_5_15_0_MinGW_64_bit-Debug/CMakeFiles/3.30.5/CMakeSystem.cmake"}, {"isGenerated": true, "path": "build/Desktop_Qt_5_15_0_MinGW_64_bit-Debug/.qtc/package-manager/maintenance_tool_provider.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeSystemSpecificInitialize.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/Platform/Windows-Initialize.cmake"}, {"isGenerated": true, "path": "build/Desktop_Qt_5_15_0_MinGW_64_bit-Debug/CMakeFiles/3.30.5/CMakeCCompiler.cmake"}, {"isGenerated": true, "path": "build/Desktop_Qt_5_15_0_MinGW_64_bit-Debug/CMakeFiles/3.30.5/CMakeCXXCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeSystemSpecificInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeGenericSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeInitializeConfigs.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/Platform/Windows.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/Platform/WindowsPaths.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeCInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/GNU-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/Platform/Windows-GNU-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/Platform/Windows-GNU.cmake"}, {"isGenerated": true, "path": "build/Desktop_Qt_5_15_0_MinGW_64_bit-Debug/CMakeFiles/3.30.5/CMakeRCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeRCInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/Platform/Windows-windres.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/Platform/Windows-GNU-C-ABI.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeCommonLanguageInclude.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeCXXInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/GNU-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/Platform/Windows-GNU-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/Platform/Windows-GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/Platform/Windows-GNU-CXX-ABI.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeCommonLanguageInclude.cmake"}, {"isExternal": true, "path": "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Core/Qt5CoreConfigVersion.cmake"}, {"isExternal": true, "path": "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Core/Qt5CoreConfig.cmake"}, {"isExternal": true, "path": "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Core/Qt5CoreConfigExtras.cmake"}, {"isExternal": true, "path": "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Core/Qt5CoreConfigExtrasMkspecDir.cmake"}, {"isExternal": true, "path": "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Core/Qt5CoreMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeParseArguments.cmake"}, {"isExternal": true, "path": "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Gui/Qt5GuiConfigVersion.cmake"}, {"isExternal": true, "path": "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Gui/Qt5GuiConfig.cmake"}, {"isExternal": true, "path": "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Gui/Qt5Gui_QGifPlugin.cmake"}, {"isExternal": true, "path": "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Gui/Qt5Gui_QICNSPlugin.cmake"}, {"isExternal": true, "path": "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Gui/Qt5Gui_QICOPlugin.cmake"}, {"isExternal": true, "path": "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Gui/Qt5Gui_QJpegPlugin.cmake"}, {"isExternal": true, "path": "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Gui/Qt5Gui_QMinimalIntegrationPlugin.cmake"}, {"isExternal": true, "path": "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Gui/Qt5Gui_QOffscreenIntegrationPlugin.cmake"}, {"isExternal": true, "path": "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Gui/Qt5Gui_QSvgIconPlugin.cmake"}, {"isExternal": true, "path": "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Gui/Qt5Gui_QSvgPlugin.cmake"}, {"isExternal": true, "path": "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Gui/Qt5Gui_QTgaPlugin.cmake"}, {"isExternal": true, "path": "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Gui/Qt5Gui_QTiffPlugin.cmake"}, {"isExternal": true, "path": "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Gui/Qt5Gui_QTuioTouchPlugin.cmake"}, {"isExternal": true, "path": "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Gui/Qt5Gui_QVirtualKeyboardPlugin.cmake"}, {"isExternal": true, "path": "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Gui/Qt5Gui_QWbmpPlugin.cmake"}, {"isExternal": true, "path": "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Gui/Qt5Gui_QWebGLIntegrationPlugin.cmake"}, {"isExternal": true, "path": "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Gui/Qt5Gui_QWebpPlugin.cmake"}, {"isExternal": true, "path": "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Gui/Qt5Gui_QWindowsDirect2DIntegrationPlugin.cmake"}, {"isExternal": true, "path": "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Gui/Qt5Gui_QWindowsIntegrationPlugin.cmake"}, {"isExternal": true, "path": "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Gui/Qt5Gui_QXdgDesktopPortalThemePlugin.cmake"}, {"isExternal": true, "path": "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Gui/Qt5GuiConfigExtras.cmake"}, {"isExternal": true, "path": "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Widgets/Qt5WidgetsConfigVersion.cmake"}, {"isExternal": true, "path": "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Widgets/Qt5WidgetsConfig.cmake"}, {"isExternal": true, "path": "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Widgets/Qt5Widgets_QWindowsVistaStylePlugin.cmake"}, {"isExternal": true, "path": "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Widgets/Qt5WidgetsConfigExtras.cmake"}, {"isExternal": true, "path": "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Widgets/Qt5WidgetsMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeParseArguments.cmake"}, {"isExternal": true, "path": "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5PrintSupport/Qt5PrintSupportConfigVersion.cmake"}, {"isExternal": true, "path": "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5PrintSupport/Qt5PrintSupportConfig.cmake"}, {"isExternal": true, "path": "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5PrintSupport/Qt5PrintSupport_QWindowsPrinterSupportPlugin.cmake"}, {"isExternal": true, "path": "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5OpenGL/Qt5OpenGLConfigVersion.cmake"}, {"isExternal": true, "path": "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5OpenGL/Qt5OpenGLConfig.cmake"}, {"isExternal": true, "path": "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5SerialPort/Qt5SerialPortConfigVersion.cmake"}, {"isExternal": true, "path": "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5SerialPort/Qt5SerialPortConfig.cmake"}, {"isExternal": true, "path": "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5SerialBus/Qt5SerialBusConfigVersion.cmake"}, {"isExternal": true, "path": "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5SerialBus/Qt5SerialBusConfig.cmake"}, {"isExternal": true, "path": "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5SerialBus/Qt5SerialBus_PassThruCanBusPlugin.cmake"}, {"isExternal": true, "path": "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5SerialBus/Qt5SerialBus_PeakCanBusPlugin.cmake"}, {"isExternal": true, "path": "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5SerialBus/Qt5SerialBus_SystecCanBusPlugin.cmake"}, {"isExternal": true, "path": "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5SerialBus/Qt5SerialBus_TinyCanBusPlugin.cmake"}, {"isExternal": true, "path": "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5SerialBus/Qt5SerialBus_VectorCanBusPlugin.cmake"}, {"isExternal": true, "path": "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5SerialBus/Qt5SerialBus_VirtualCanBusPlugin.cmake"}, {"isExternal": true, "path": "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5/Qt5ConfigVersion.cmake"}, {"isExternal": true, "path": "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5/Qt5Config.cmake"}, {"isExternal": true, "path": "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5/Qt5ModuleLocation.cmake"}, {"isExternal": true, "path": "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Core/Qt5CoreConfigVersion.cmake"}, {"isExternal": true, "path": "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Core/Qt5CoreConfig.cmake"}, {"isExternal": true, "path": "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Gui/Qt5GuiConfigVersion.cmake"}, {"isExternal": true, "path": "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Gui/Qt5GuiConfig.cmake"}, {"isExternal": true, "path": "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Widgets/Qt5WidgetsConfigVersion.cmake"}, {"isExternal": true, "path": "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Widgets/Qt5WidgetsConfig.cmake"}, {"isExternal": true, "path": "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5PrintSupport/Qt5PrintSupportConfigVersion.cmake"}, {"isExternal": true, "path": "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5PrintSupport/Qt5PrintSupportConfig.cmake"}, {"isExternal": true, "path": "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5OpenGL/Qt5OpenGLConfigVersion.cmake"}, {"isExternal": true, "path": "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5OpenGL/Qt5OpenGLConfig.cmake"}, {"isExternal": true, "path": "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5SerialPort/Qt5SerialPortConfigVersion.cmake"}, {"isExternal": true, "path": "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5SerialPort/Qt5SerialPortConfig.cmake"}, {"isExternal": true, "path": "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5SerialBus/Qt5SerialBusConfigVersion.cmake"}, {"isExternal": true, "path": "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5SerialBus/Qt5SerialBusConfig.cmake"}], "kind": "cmakeFiles", "paths": {"build": "C:/Users/<USER>/Desktop/TempDetectQt/build/Desktop_Qt_5_15_0_MinGW_64_bit-Debug", "source": "C:/Users/<USER>/Desktop/TempDetectQt"}, "version": {"major": 1, "minor": 1}}