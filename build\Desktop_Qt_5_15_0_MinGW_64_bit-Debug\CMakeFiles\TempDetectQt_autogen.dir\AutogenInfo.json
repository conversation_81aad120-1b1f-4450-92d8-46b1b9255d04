{"AUTOGEN_COMMAND_LINE_LENGTH_MAX": 32000, "BUILD_DIR": "C:/Users/<USER>/Desktop/TempDetectQt/build/Desktop_Qt_5_15_0_MinGW_64_bit-Debug/TempDetectQt_autogen", "CMAKE_BINARY_DIR": "C:/Users/<USER>/Desktop/TempDetectQt/build/Desktop_Qt_5_15_0_MinGW_64_bit-Debug", "CMAKE_CURRENT_BINARY_DIR": "C:/Users/<USER>/Desktop/TempDetectQt/build/Desktop_Qt_5_15_0_MinGW_64_bit-Debug", "CMAKE_CURRENT_SOURCE_DIR": "C:/Users/<USER>/Desktop/TempDetectQt", "CMAKE_EXECUTABLE": "C:/Qt/Tools/CMake_64/bin/cmake.exe", "CMAKE_LIST_FILES": ["C:/Users/<USER>/Desktop/TempDetectQt/CMakeLists.txt", "C:/Users/<USER>/Desktop/TempDetectQt/build/Desktop_Qt_5_15_0_MinGW_64_bit-Debug/.qtc/package-manager/auto-setup.cmake", "C:/Users/<USER>/Desktop/TempDetectQt/build/Desktop_Qt_5_15_0_MinGW_64_bit-Debug/CMakeFiles/3.30.5/CMakeSystem.cmake", "C:/Users/<USER>/Desktop/TempDetectQt/build/Desktop_Qt_5_15_0_MinGW_64_bit-Debug/.qtc/package-manager/maintenance_tool_provider.cmake", "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeSystemSpecificInitialize.cmake", "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/Platform/Windows-Initialize.cmake", "C:/Users/<USER>/Desktop/TempDetectQt/build/Desktop_Qt_5_15_0_MinGW_64_bit-Debug/CMakeFiles/3.30.5/CMakeCCompiler.cmake", "C:/Users/<USER>/Desktop/TempDetectQt/build/Desktop_Qt_5_15_0_MinGW_64_bit-Debug/CMakeFiles/3.30.5/CMakeCXXCompiler.cmake", "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeSystemSpecificInformation.cmake", "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeGenericSystem.cmake", "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeInitializeConfigs.cmake", "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/Platform/Windows.cmake", "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/Platform/WindowsPaths.cmake", "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeCInformation.cmake", "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeLanguageInformation.cmake", "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/GNU-C.cmake", "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/GNU.cmake", "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/CMakeCommonCompilerMacros.cmake", "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/Platform/Windows-GNU-C.cmake", "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/Platform/Windows-GNU.cmake", "C:/Users/<USER>/Desktop/TempDetectQt/build/Desktop_Qt_5_15_0_MinGW_64_bit-Debug/CMakeFiles/3.30.5/CMakeRCCompiler.cmake", "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeRCInformation.cmake", "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/Platform/Windows-windres.cmake", "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/Platform/Windows-GNU-C-ABI.cmake", "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeCommonLanguageInclude.cmake", "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeCXXInformation.cmake", "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeLanguageInformation.cmake", "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/GNU-CXX.cmake", "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/Compiler/GNU.cmake", "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/Platform/Windows-GNU-CXX.cmake", "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/Platform/Windows-GNU.cmake", "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/Platform/Windows-GNU-CXX-ABI.cmake", "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeCommonLanguageInclude.cmake", "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Core/Qt5CoreConfigVersion.cmake", "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Core/Qt5CoreConfig.cmake", "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Core/Qt5CoreConfigExtras.cmake", "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Core/Qt5CoreConfigExtrasMkspecDir.cmake", "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Core/Qt5CoreMacros.cmake", "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeParseArguments.cmake", "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Gui/Qt5GuiConfigVersion.cmake", "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Gui/Qt5GuiConfig.cmake", "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Gui/Qt5Gui_QGifPlugin.cmake", "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Gui/Qt5Gui_QICNSPlugin.cmake", "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Gui/Qt5Gui_QICOPlugin.cmake", "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Gui/Qt5Gui_QJpegPlugin.cmake", "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Gui/Qt5Gui_QMinimalIntegrationPlugin.cmake", "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Gui/Qt5Gui_QOffscreenIntegrationPlugin.cmake", "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Gui/Qt5Gui_QSvgIconPlugin.cmake", "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Gui/Qt5Gui_QSvgPlugin.cmake", "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Gui/Qt5Gui_QTgaPlugin.cmake", "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Gui/Qt5Gui_QTiffPlugin.cmake", "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Gui/Qt5Gui_QTuioTouchPlugin.cmake", "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Gui/Qt5Gui_QVirtualKeyboardPlugin.cmake", "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Gui/Qt5Gui_QWbmpPlugin.cmake", "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Gui/Qt5Gui_QWebGLIntegrationPlugin.cmake", "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Gui/Qt5Gui_QWebpPlugin.cmake", "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Gui/Qt5Gui_QWindowsDirect2DIntegrationPlugin.cmake", "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Gui/Qt5Gui_QWindowsIntegrationPlugin.cmake", "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Gui/Qt5Gui_QXdgDesktopPortalThemePlugin.cmake", "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Gui/Qt5GuiConfigExtras.cmake", "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Widgets/Qt5WidgetsConfigVersion.cmake", "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Widgets/Qt5WidgetsConfig.cmake", "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Widgets/Qt5Widgets_QWindowsVistaStylePlugin.cmake", "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Widgets/Qt5WidgetsConfigExtras.cmake", "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Widgets/Qt5WidgetsMacros.cmake", "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeParseArguments.cmake", "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5PrintSupport/Qt5PrintSupportConfigVersion.cmake", "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5PrintSupport/Qt5PrintSupportConfig.cmake", "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5PrintSupport/Qt5PrintSupport_QWindowsPrinterSupportPlugin.cmake", "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5OpenGL/Qt5OpenGLConfigVersion.cmake", "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5OpenGL/Qt5OpenGLConfig.cmake", "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5SerialPort/Qt5SerialPortConfigVersion.cmake", "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5SerialPort/Qt5SerialPortConfig.cmake", "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5SerialBus/Qt5SerialBusConfigVersion.cmake", "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5SerialBus/Qt5SerialBusConfig.cmake", "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5SerialBus/Qt5SerialBus_PassThruCanBusPlugin.cmake", "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5SerialBus/Qt5SerialBus_PeakCanBusPlugin.cmake", "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5SerialBus/Qt5SerialBus_SystecCanBusPlugin.cmake", "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5SerialBus/Qt5SerialBus_TinyCanBusPlugin.cmake", "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5SerialBus/Qt5SerialBus_VectorCanBusPlugin.cmake", "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5SerialBus/Qt5SerialBus_VirtualCanBusPlugin.cmake", "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5/Qt5ConfigVersion.cmake", "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5/Qt5Config.cmake", "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5/Qt5ModuleLocation.cmake", "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Core/Qt5CoreConfigVersion.cmake", "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Core/Qt5CoreConfig.cmake", "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Gui/Qt5GuiConfigVersion.cmake", "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Gui/Qt5GuiConfig.cmake", "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Widgets/Qt5WidgetsConfigVersion.cmake", "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5Widgets/Qt5WidgetsConfig.cmake", "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5PrintSupport/Qt5PrintSupportConfigVersion.cmake", "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5PrintSupport/Qt5PrintSupportConfig.cmake", "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5OpenGL/Qt5OpenGLConfigVersion.cmake", "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5OpenGL/Qt5OpenGLConfig.cmake", "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5SerialPort/Qt5SerialPortConfigVersion.cmake", "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5SerialPort/Qt5SerialPortConfig.cmake", "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5SerialBus/Qt5SerialBusConfigVersion.cmake", "C:/Qt/5.15.0/msvc2019_64/lib/cmake/Qt5SerialBus/Qt5SerialBusConfig.cmake"], "CMAKE_SOURCE_DIR": "C:/Users/<USER>/Desktop/TempDetectQt", "CROSS_CONFIG": false, "DEP_FILE": "C:/Users/<USER>/Desktop/TempDetectQt/build/Desktop_Qt_5_15_0_MinGW_64_bit-Debug/TempDetectQt_autogen/deps", "DEP_FILE_RULE_NAME": "TempDetectQt_autogen/timestamp", "HEADERS": [["C:/Users/<USER>/Desktop/TempDetectQt/mainwindow.h", "MU", "EWIEGA46WW/moc_mainwindow.cpp", null], ["C:/Users/<USER>/Desktop/TempDetectQt/modbusmanager.h", "MU", "EWIEGA46WW/moc_modbusmanager.cpp", null], ["C:/Users/<USER>/Desktop/TempDetectQt/qcustomplot.h", "MU", "EWIEGA46WW/moc_qcustomplot.cpp", null], ["C:/Users/<USER>/Desktop/TempDetectQt/temperaturedata.h", "MU", "EWIEGA46WW/moc_temperaturedata.cpp", null]], "HEADER_EXTENSIONS": ["h", "hh", "h++", "hm", "hpp", "hxx", "in", "txx"], "INCLUDE_DIR": "C:/Users/<USER>/Desktop/TempDetectQt/build/Desktop_Qt_5_15_0_MinGW_64_bit-Debug/TempDetectQt_autogen/include", "MOC_COMPILATION_FILE": "C:/Users/<USER>/Desktop/TempDetectQt/build/Desktop_Qt_5_15_0_MinGW_64_bit-Debug/TempDetectQt_autogen/mocs_compilation.cpp", "MOC_DEFINITIONS": ["QT_CORE_LIB", "QT_GUI_LIB", "QT_OPENGL_LIB", "QT_PRINTSUPPORT_LIB", "QT_SERIALBUS_LIB", "QT_SERIALPORT_LIB", "QT_WIDGETS_LIB"], "MOC_DEPEND_FILTERS": [["Q_PLUGIN_METADATA", "[\n][ \t]*Q_PLUGIN_METADATA[ \t]*\\([^\\)]*FILE[ \t]*\"([^\"]+)\""]], "MOC_INCLUDES": ["C:/Users/<USER>/Desktop/TempDetectQt", "/opt/homebrew/Cellar/qt@5/5.15.16/include", "/opt/homebrew/Cellar/qt@5/5.15.16/include/QtSerialBus", "/opt/homebrew/Cellar/qt@5/5.15.16/include/QtSerialPort", "C:/Qt/5.15.0/msvc2019_64/include", "C:/Qt/5.15.0/msvc2019_64/include/QtCore", "C:/Qt/5.15.0/msvc2019_64/mkspecs/win32-msvc", "C:/Qt/5.15.0/msvc2019_64/include/QtGui", "C:/Qt/5.15.0/msvc2019_64/include/QtANGLE", "C:/Qt/5.15.0/msvc2019_64/include/QtWidgets", "C:/Qt/5.15.0/msvc2019_64/include/QtPrintSupport", "C:/Qt/5.15.0/msvc2019_64/include/QtOpenGL", "C:/Qt/5.15.0/msvc2019_64/include/QtSerialPort", "C:/Qt/5.15.0/msvc2019_64/include/QtSerialBus", "C:/Qt/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++", "C:/Qt/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/x86_64-w64-mingw32", "C:/Qt/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/backward", "C:/Qt/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include", "C:/Qt/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include-fixed", "C:/Qt/Tools/mingw810_64/x86_64-w64-mingw32/include"], "MOC_MACRO_NAMES": ["Q_OBJECT", "Q_GADGET", "Q_NAMESPACE", "Q_NAMESPACE_EXPORT"], "MOC_OPTIONS": [], "MOC_PATH_PREFIX": false, "MOC_PREDEFS_CMD": ["C:/Qt/Tools/mingw810_64/bin/g++.exe", "-std=gnu++17", "-dM", "-E", "-c", "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeCXXCompilerABI.cpp"], "MOC_PREDEFS_FILE": "C:/Users/<USER>/Desktop/TempDetectQt/build/Desktop_Qt_5_15_0_MinGW_64_bit-Debug/TempDetectQt_autogen/moc_predefs.h", "MOC_RELAXED_MODE": false, "MOC_SKIP": ["C:/Users/<USER>/Desktop/TempDetectQt/build/Desktop_Qt_5_15_0_MinGW_64_bit-Debug/ui_mainwindow.h"], "MULTI_CONFIG": false, "PARALLEL": 24, "PARSE_CACHE_FILE": "C:/Users/<USER>/Desktop/TempDetectQt/build/Desktop_Qt_5_15_0_MinGW_64_bit-Debug/CMakeFiles/TempDetectQt_autogen.dir/ParseCache.txt", "QT_MOC_EXECUTABLE": "C:/Qt/5.15.0/msvc2019_64/bin/moc.exe", "QT_UIC_EXECUTABLE": "C:/Qt/5.15.0/msvc2019_64/bin/uic.exe", "QT_VERSION_MAJOR": 5, "QT_VERSION_MINOR": 15, "SETTINGS_FILE": "C:/Users/<USER>/Desktop/TempDetectQt/build/Desktop_Qt_5_15_0_MinGW_64_bit-Debug/CMakeFiles/TempDetectQt_autogen.dir/AutogenUsed.txt", "SOURCES": [["C:/Users/<USER>/Desktop/TempDetectQt/main.cpp", "MU", null], ["C:/Users/<USER>/Desktop/TempDetectQt/mainwindow.cpp", "MU", null], ["C:/Users/<USER>/Desktop/TempDetectQt/modbusmanager.cpp", "MU", null], ["C:/Users/<USER>/Desktop/TempDetectQt/qcustomplot.cpp", "MU", null], ["C:/Users/<USER>/Desktop/TempDetectQt/temperaturedata.cpp", "MU", null]], "UIC_OPTIONS": [], "UIC_SEARCH_PATHS": [], "UIC_SKIP": ["C:/Users/<USER>/Desktop/TempDetectQt/build/Desktop_Qt_5_15_0_MinGW_64_bit-Debug/ui_mainwindow.h", "C:/Users/<USER>/Desktop/TempDetectQt/mainwindow.ui"], "UIC_UI_FILES": [], "USE_BETTER_GRAPH": false, "VERBOSITY": 0}