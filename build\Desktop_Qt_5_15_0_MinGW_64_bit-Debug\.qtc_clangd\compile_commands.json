[{"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_OPENGL_LIB", "-DQT_PRINTSUPPORT_LIB", "-DQT_SERIALBUS_LIB", "-DQT_SERIALPORT_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Desktop\\TempDetectQt\\build\\Desktop_Qt_5_15_0_MinGW_64_bit-Debug\\TempDetectQt_autogen\\include", "-IC:\\Users\\<USER>\\Desktop\\TempDetectQt", "-I\\opt\\homebrew\\Cellar\\qt@5\\5.15.16\\include", "-I\\opt\\homebrew\\Cellar\\qt@5\\5.15.16\\include\\QtSerialBus", "-I\\opt\\homebrew\\Cellar\\qt@5\\5.15.16\\include\\QtSerialPort", "-isystem", "C:\\Qt\\5.15.0\\msvc2019_64\\include", "-isystem", "C:\\Qt\\5.15.0\\msvc2019_64\\include\\QtCore", "-isystem", "C:\\Qt\\5.15.0\\msvc2019_64\\mkspecs\\win32-msvc", "-isystem", "C:\\Qt\\5.15.0\\msvc2019_64\\include\\QtGui", "-isystem", "C:\\Qt\\5.15.0\\msvc2019_64\\include\\QtANGLE", "-isystem", "C:\\Qt\\5.15.0\\msvc2019_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\5.15.0\\msvc2019_64\\include\\QtPrintSupport", "-isystem", "C:\\Qt\\5.15.0\\msvc2019_64\\include\\QtOpenGL", "-isystem", "C:\\Qt\\5.15.0\\msvc2019_64\\include\\QtSerialPort", "-isystem", "C:\\Qt\\5.15.0\\msvc2019_64\\include\\QtSerialBus", "-isystem", "C:\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw810_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "C:\\Users\\<USER>\\Desktop\\TempDetectQt\\main.cpp"], "directory": "C:/Users/<USER>/Desktop/TempDetectQt/build/Desktop_Qt_5_15_0_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Desktop/TempDetectQt/main.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_OPENGL_LIB", "-DQT_PRINTSUPPORT_LIB", "-DQT_SERIALBUS_LIB", "-DQT_SERIALPORT_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Desktop\\TempDetectQt\\build\\Desktop_Qt_5_15_0_MinGW_64_bit-Debug\\TempDetectQt_autogen\\include", "-IC:\\Users\\<USER>\\Desktop\\TempDetectQt", "-I\\opt\\homebrew\\Cellar\\qt@5\\5.15.16\\include", "-I\\opt\\homebrew\\Cellar\\qt@5\\5.15.16\\include\\QtSerialBus", "-I\\opt\\homebrew\\Cellar\\qt@5\\5.15.16\\include\\QtSerialPort", "-isystem", "C:\\Qt\\5.15.0\\msvc2019_64\\include", "-isystem", "C:\\Qt\\5.15.0\\msvc2019_64\\include\\QtCore", "-isystem", "C:\\Qt\\5.15.0\\msvc2019_64\\mkspecs\\win32-msvc", "-isystem", "C:\\Qt\\5.15.0\\msvc2019_64\\include\\QtGui", "-isystem", "C:\\Qt\\5.15.0\\msvc2019_64\\include\\QtANGLE", "-isystem", "C:\\Qt\\5.15.0\\msvc2019_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\5.15.0\\msvc2019_64\\include\\QtPrintSupport", "-isystem", "C:\\Qt\\5.15.0\\msvc2019_64\\include\\QtOpenGL", "-isystem", "C:\\Qt\\5.15.0\\msvc2019_64\\include\\QtSerialPort", "-isystem", "C:\\Qt\\5.15.0\\msvc2019_64\\include\\QtSerialBus", "-isystem", "C:\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw810_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "C:\\Users\\<USER>\\Desktop\\TempDetectQt\\mainwindow.cpp"], "directory": "C:/Users/<USER>/Desktop/TempDetectQt/build/Desktop_Qt_5_15_0_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Desktop/TempDetectQt/mainwindow.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_OPENGL_LIB", "-DQT_PRINTSUPPORT_LIB", "-DQT_SERIALBUS_LIB", "-DQT_SERIALPORT_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Desktop\\TempDetectQt\\build\\Desktop_Qt_5_15_0_MinGW_64_bit-Debug\\TempDetectQt_autogen\\include", "-IC:\\Users\\<USER>\\Desktop\\TempDetectQt", "-I\\opt\\homebrew\\Cellar\\qt@5\\5.15.16\\include", "-I\\opt\\homebrew\\Cellar\\qt@5\\5.15.16\\include\\QtSerialBus", "-I\\opt\\homebrew\\Cellar\\qt@5\\5.15.16\\include\\QtSerialPort", "-isystem", "C:\\Qt\\5.15.0\\msvc2019_64\\include", "-isystem", "C:\\Qt\\5.15.0\\msvc2019_64\\include\\QtCore", "-isystem", "C:\\Qt\\5.15.0\\msvc2019_64\\mkspecs\\win32-msvc", "-isystem", "C:\\Qt\\5.15.0\\msvc2019_64\\include\\QtGui", "-isystem", "C:\\Qt\\5.15.0\\msvc2019_64\\include\\QtANGLE", "-isystem", "C:\\Qt\\5.15.0\\msvc2019_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\5.15.0\\msvc2019_64\\include\\QtPrintSupport", "-isystem", "C:\\Qt\\5.15.0\\msvc2019_64\\include\\QtOpenGL", "-isystem", "C:\\Qt\\5.15.0\\msvc2019_64\\include\\QtSerialPort", "-isystem", "C:\\Qt\\5.15.0\\msvc2019_64\\include\\QtSerialBus", "-isystem", "C:\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw810_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "C:\\Users\\<USER>\\Desktop\\TempDetectQt\\modbusmanager.cpp"], "directory": "C:/Users/<USER>/Desktop/TempDetectQt/build/Desktop_Qt_5_15_0_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Desktop/TempDetectQt/modbusmanager.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_OPENGL_LIB", "-DQT_PRINTSUPPORT_LIB", "-DQT_SERIALBUS_LIB", "-DQT_SERIALPORT_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Desktop\\TempDetectQt\\build\\Desktop_Qt_5_15_0_MinGW_64_bit-Debug\\TempDetectQt_autogen\\include", "-IC:\\Users\\<USER>\\Desktop\\TempDetectQt", "-I\\opt\\homebrew\\Cellar\\qt@5\\5.15.16\\include", "-I\\opt\\homebrew\\Cellar\\qt@5\\5.15.16\\include\\QtSerialBus", "-I\\opt\\homebrew\\Cellar\\qt@5\\5.15.16\\include\\QtSerialPort", "-isystem", "C:\\Qt\\5.15.0\\msvc2019_64\\include", "-isystem", "C:\\Qt\\5.15.0\\msvc2019_64\\include\\QtCore", "-isystem", "C:\\Qt\\5.15.0\\msvc2019_64\\mkspecs\\win32-msvc", "-isystem", "C:\\Qt\\5.15.0\\msvc2019_64\\include\\QtGui", "-isystem", "C:\\Qt\\5.15.0\\msvc2019_64\\include\\QtANGLE", "-isystem", "C:\\Qt\\5.15.0\\msvc2019_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\5.15.0\\msvc2019_64\\include\\QtPrintSupport", "-isystem", "C:\\Qt\\5.15.0\\msvc2019_64\\include\\QtOpenGL", "-isystem", "C:\\Qt\\5.15.0\\msvc2019_64\\include\\QtSerialPort", "-isystem", "C:\\Qt\\5.15.0\\msvc2019_64\\include\\QtSerialBus", "-isystem", "C:\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw810_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "C:\\Users\\<USER>\\Desktop\\TempDetectQt\\qcustomplot.cpp"], "directory": "C:/Users/<USER>/Desktop/TempDetectQt/build/Desktop_Qt_5_15_0_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Desktop/TempDetectQt/qcustomplot.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_OPENGL_LIB", "-DQT_PRINTSUPPORT_LIB", "-DQT_SERIALBUS_LIB", "-DQT_SERIALPORT_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Desktop\\TempDetectQt\\build\\Desktop_Qt_5_15_0_MinGW_64_bit-Debug\\TempDetectQt_autogen\\include", "-IC:\\Users\\<USER>\\Desktop\\TempDetectQt", "-I\\opt\\homebrew\\Cellar\\qt@5\\5.15.16\\include", "-I\\opt\\homebrew\\Cellar\\qt@5\\5.15.16\\include\\QtSerialBus", "-I\\opt\\homebrew\\Cellar\\qt@5\\5.15.16\\include\\QtSerialPort", "-isystem", "C:\\Qt\\5.15.0\\msvc2019_64\\include", "-isystem", "C:\\Qt\\5.15.0\\msvc2019_64\\include\\QtCore", "-isystem", "C:\\Qt\\5.15.0\\msvc2019_64\\mkspecs\\win32-msvc", "-isystem", "C:\\Qt\\5.15.0\\msvc2019_64\\include\\QtGui", "-isystem", "C:\\Qt\\5.15.0\\msvc2019_64\\include\\QtANGLE", "-isystem", "C:\\Qt\\5.15.0\\msvc2019_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\5.15.0\\msvc2019_64\\include\\QtPrintSupport", "-isystem", "C:\\Qt\\5.15.0\\msvc2019_64\\include\\QtOpenGL", "-isystem", "C:\\Qt\\5.15.0\\msvc2019_64\\include\\QtSerialPort", "-isystem", "C:\\Qt\\5.15.0\\msvc2019_64\\include\\QtSerialBus", "-isystem", "C:\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw810_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "C:\\Users\\<USER>\\Desktop\\TempDetectQt\\temperaturedata.cpp"], "directory": "C:/Users/<USER>/Desktop/TempDetectQt/build/Desktop_Qt_5_15_0_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Desktop/TempDetectQt/temperaturedata.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_OPENGL_LIB", "-DQT_PRINTSUPPORT_LIB", "-DQT_SERIALBUS_LIB", "-DQT_SERIALPORT_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Desktop\\TempDetectQt\\build\\Desktop_Qt_5_15_0_MinGW_64_bit-Debug\\TempDetectQt_autogen\\include", "-IC:\\Users\\<USER>\\Desktop\\TempDetectQt", "-I\\opt\\homebrew\\Cellar\\qt@5\\5.15.16\\include", "-I\\opt\\homebrew\\Cellar\\qt@5\\5.15.16\\include\\QtSerialBus", "-I\\opt\\homebrew\\Cellar\\qt@5\\5.15.16\\include\\QtSerialPort", "-isystem", "C:\\Qt\\5.15.0\\msvc2019_64\\include", "-isystem", "C:\\Qt\\5.15.0\\msvc2019_64\\include\\QtCore", "-isystem", "C:\\Qt\\5.15.0\\msvc2019_64\\mkspecs\\win32-msvc", "-isystem", "C:\\Qt\\5.15.0\\msvc2019_64\\include\\QtGui", "-isystem", "C:\\Qt\\5.15.0\\msvc2019_64\\include\\QtANGLE", "-isystem", "C:\\Qt\\5.15.0\\msvc2019_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\5.15.0\\msvc2019_64\\include\\QtPrintSupport", "-isystem", "C:\\Qt\\5.15.0\\msvc2019_64\\include\\QtOpenGL", "-isystem", "C:\\Qt\\5.15.0\\msvc2019_64\\include\\QtSerialPort", "-isystem", "C:\\Qt\\5.15.0\\msvc2019_64\\include\\QtSerialBus", "-isystem", "C:\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw810_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "C:\\Users\\<USER>\\Desktop\\TempDetectQt\\mainwindow.h"], "directory": "C:/Users/<USER>/Desktop/TempDetectQt/build/Desktop_Qt_5_15_0_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Desktop/TempDetectQt/mainwindow.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_OPENGL_LIB", "-DQT_PRINTSUPPORT_LIB", "-DQT_SERIALBUS_LIB", "-DQT_SERIALPORT_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Desktop\\TempDetectQt\\build\\Desktop_Qt_5_15_0_MinGW_64_bit-Debug\\TempDetectQt_autogen\\include", "-IC:\\Users\\<USER>\\Desktop\\TempDetectQt", "-I\\opt\\homebrew\\Cellar\\qt@5\\5.15.16\\include", "-I\\opt\\homebrew\\Cellar\\qt@5\\5.15.16\\include\\QtSerialBus", "-I\\opt\\homebrew\\Cellar\\qt@5\\5.15.16\\include\\QtSerialPort", "-isystem", "C:\\Qt\\5.15.0\\msvc2019_64\\include", "-isystem", "C:\\Qt\\5.15.0\\msvc2019_64\\include\\QtCore", "-isystem", "C:\\Qt\\5.15.0\\msvc2019_64\\mkspecs\\win32-msvc", "-isystem", "C:\\Qt\\5.15.0\\msvc2019_64\\include\\QtGui", "-isystem", "C:\\Qt\\5.15.0\\msvc2019_64\\include\\QtANGLE", "-isystem", "C:\\Qt\\5.15.0\\msvc2019_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\5.15.0\\msvc2019_64\\include\\QtPrintSupport", "-isystem", "C:\\Qt\\5.15.0\\msvc2019_64\\include\\QtOpenGL", "-isystem", "C:\\Qt\\5.15.0\\msvc2019_64\\include\\QtSerialPort", "-isystem", "C:\\Qt\\5.15.0\\msvc2019_64\\include\\QtSerialBus", "-isystem", "C:\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw810_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "C:\\Users\\<USER>\\Desktop\\TempDetectQt\\qcustomplot.h"], "directory": "C:/Users/<USER>/Desktop/TempDetectQt/build/Desktop_Qt_5_15_0_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Desktop/TempDetectQt/qcustomplot.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_OPENGL_LIB", "-DQT_PRINTSUPPORT_LIB", "-DQT_SERIALBUS_LIB", "-DQT_SERIALPORT_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Desktop\\TempDetectQt\\build\\Desktop_Qt_5_15_0_MinGW_64_bit-Debug\\TempDetectQt_autogen\\include", "-IC:\\Users\\<USER>\\Desktop\\TempDetectQt", "-I\\opt\\homebrew\\Cellar\\qt@5\\5.15.16\\include", "-I\\opt\\homebrew\\Cellar\\qt@5\\5.15.16\\include\\QtSerialBus", "-I\\opt\\homebrew\\Cellar\\qt@5\\5.15.16\\include\\QtSerialPort", "-isystem", "C:\\Qt\\5.15.0\\msvc2019_64\\include", "-isystem", "C:\\Qt\\5.15.0\\msvc2019_64\\include\\QtCore", "-isystem", "C:\\Qt\\5.15.0\\msvc2019_64\\mkspecs\\win32-msvc", "-isystem", "C:\\Qt\\5.15.0\\msvc2019_64\\include\\QtGui", "-isystem", "C:\\Qt\\5.15.0\\msvc2019_64\\include\\QtANGLE", "-isystem", "C:\\Qt\\5.15.0\\msvc2019_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\5.15.0\\msvc2019_64\\include\\QtPrintSupport", "-isystem", "C:\\Qt\\5.15.0\\msvc2019_64\\include\\QtOpenGL", "-isystem", "C:\\Qt\\5.15.0\\msvc2019_64\\include\\QtSerialPort", "-isystem", "C:\\Qt\\5.15.0\\msvc2019_64\\include\\QtSerialBus", "-isystem", "C:\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw810_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "C:\\Users\\<USER>\\Desktop\\TempDetectQt\\modbusmanager.h"], "directory": "C:/Users/<USER>/Desktop/TempDetectQt/build/Desktop_Qt_5_15_0_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Desktop/TempDetectQt/modbusmanager.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_OPENGL_LIB", "-DQT_PRINTSUPPORT_LIB", "-DQT_SERIALBUS_LIB", "-DQT_SERIALPORT_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Users\\<USER>\\Desktop\\TempDetectQt\\build\\Desktop_Qt_5_15_0_MinGW_64_bit-Debug\\TempDetectQt_autogen\\include", "-IC:\\Users\\<USER>\\Desktop\\TempDetectQt", "-I\\opt\\homebrew\\Cellar\\qt@5\\5.15.16\\include", "-I\\opt\\homebrew\\Cellar\\qt@5\\5.15.16\\include\\QtSerialBus", "-I\\opt\\homebrew\\Cellar\\qt@5\\5.15.16\\include\\QtSerialPort", "-isystem", "C:\\Qt\\5.15.0\\msvc2019_64\\include", "-isystem", "C:\\Qt\\5.15.0\\msvc2019_64\\include\\QtCore", "-isystem", "C:\\Qt\\5.15.0\\msvc2019_64\\mkspecs\\win32-msvc", "-isystem", "C:\\Qt\\5.15.0\\msvc2019_64\\include\\QtGui", "-isystem", "C:\\Qt\\5.15.0\\msvc2019_64\\include\\QtANGLE", "-isystem", "C:\\Qt\\5.15.0\\msvc2019_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\5.15.0\\msvc2019_64\\include\\QtPrintSupport", "-isystem", "C:\\Qt\\5.15.0\\msvc2019_64\\include\\QtOpenGL", "-isystem", "C:\\Qt\\5.15.0\\msvc2019_64\\include\\QtSerialPort", "-isystem", "C:\\Qt\\5.15.0\\msvc2019_64\\include\\QtSerialBus", "-isystem", "C:\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw810_64\\lib\\gcc\\x86_64-w64-mingw32\\8.1.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw810_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "C:\\Users\\<USER>\\Desktop\\TempDetectQt\\temperaturedata.h"], "directory": "C:/Users/<USER>/Desktop/TempDetectQt/build/Desktop_Qt_5_15_0_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Users/<USER>/Desktop/TempDetectQt/temperaturedata.h"}]