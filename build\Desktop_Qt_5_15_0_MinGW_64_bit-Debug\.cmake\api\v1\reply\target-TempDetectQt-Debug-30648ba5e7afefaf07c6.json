{"artifacts": [{"path": "TempDetectQt.exe"}, {"path": "TempDetectQt.pdb"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "target_link_libraries", "include_directories"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 40, "parent": 0}, {"command": 1, "file": 0, "line": 51, "parent": 0}, {"command": 2, "file": 0, "line": 43, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-DQT_QML_DEBUG -g -std=gnu++17 -fdiagnostics-color=always"}], "defines": [{"backtrace": 2, "define": "QT_CORE_LIB"}, {"backtrace": 2, "define": "QT_GUI_LIB"}, {"backtrace": 2, "define": "QT_OPENGL_LIB"}, {"backtrace": 2, "define": "QT_PRINTSUPPORT_LIB"}, {"backtrace": 2, "define": "QT_SERIALBUS_LIB"}, {"backtrace": 2, "define": "QT_SERIALPORT_LIB"}, {"backtrace": 2, "define": "QT_WIDGETS_LIB"}], "includes": [{"backtrace": 0, "path": "C:/Users/<USER>/Desktop/TempDetectQt/build/Desktop_Qt_5_15_0_MinGW_64_bit-Debug/TempDetectQt_autogen/include"}, {"backtrace": 3, "path": "C:/Users/<USER>/Desktop/TempDetectQt"}, {"backtrace": 3, "path": "/opt/homebrew/Cellar/qt@5/5.15.16/include"}, {"backtrace": 3, "path": "/opt/homebrew/Cellar/qt@5/5.15.16/include/QtSerialBus"}, {"backtrace": 3, "path": "/opt/homebrew/Cellar/qt@5/5.15.16/include/QtSerialPort"}, {"backtrace": 2, "isSystem": true, "path": "C:/Qt/5.15.0/msvc2019_64/include"}, {"backtrace": 2, "isSystem": true, "path": "C:/Qt/5.15.0/msvc2019_64/include/QtCore"}, {"backtrace": 2, "isSystem": true, "path": "C:/Qt/5.15.0/msvc2019_64/./mkspecs/win32-msvc"}, {"backtrace": 2, "isSystem": true, "path": "C:/Qt/5.15.0/msvc2019_64/include/QtGui"}, {"backtrace": 2, "isSystem": true, "path": "C:/Qt/5.15.0/msvc2019_64/include/QtANGLE"}, {"backtrace": 2, "isSystem": true, "path": "C:/Qt/5.15.0/msvc2019_64/include/QtWidgets"}, {"backtrace": 2, "isSystem": true, "path": "C:/Qt/5.15.0/msvc2019_64/include/QtPrintSupport"}, {"backtrace": 2, "isSystem": true, "path": "C:/Qt/5.15.0/msvc2019_64/include/QtOpenGL"}, {"backtrace": 2, "isSystem": true, "path": "C:/Qt/5.15.0/msvc2019_64/include/QtSerialPort"}, {"backtrace": 2, "isSystem": true, "path": "C:/Qt/5.15.0/msvc2019_64/include/QtSerialBus"}], "language": "CXX", "languageStandard": {"backtraces": [2], "standard": "17"}, "sourceIndexes": [0, 1, 2, 3, 4, 5]}], "dependencies": [{"id": "TempDetectQt_autogen_timestamp_deps::@6890427a1f51a3e7e1df"}, {"backtrace": 0, "id": "TempDetectQt_autogen::@6890427a1f51a3e7e1df"}], "id": "TempDetectQt::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "-DQT_QML_DEBUG -g", "role": "flags"}, {"fragment": "", "role": "flags"}, {"backtrace": 2, "fragment": "C:\\Qt\\5.15.0\\msvc2019_64\\lib\\Qt5PrintSupportd.lib", "role": "libraries"}, {"backtrace": 2, "fragment": "C:\\Qt\\5.15.0\\msvc2019_64\\lib\\Qt5OpenGLd.lib", "role": "libraries"}, {"backtrace": 2, "fragment": "C:\\Qt\\5.15.0\\msvc2019_64\\lib\\Qt5SerialPortd.lib", "role": "libraries"}, {"backtrace": 2, "fragment": "C:\\Qt\\5.15.0\\msvc2019_64\\lib\\Qt5SerialBusd.lib", "role": "libraries"}, {"backtrace": 2, "fragment": "C:\\Qt\\5.15.0\\msvc2019_64\\lib\\Qt5Widgetsd.lib", "role": "libraries"}, {"backtrace": 2, "fragment": "C:\\Qt\\5.15.0\\msvc2019_64\\lib\\Qt5Guid.lib", "role": "libraries"}, {"backtrace": 2, "fragment": "C:\\Qt\\5.15.0\\msvc2019_64\\lib\\Qt5Cored.lib", "role": "libraries"}, {"fragment": "-lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32", "role": "libraries"}], "language": "CXX"}, "name": "TempDetectQt", "nameOnDisk": "TempDetectQt.exe", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [6, 7, 8, 9, 10]}, {"name": "", "sourceIndexes": [11]}, {"name": "Forms", "sourceIndexes": [12]}, {"name": "CMake Rules", "sourceIndexes": [13]}], "sources": [{"backtrace": 0, "compileGroupIndex": 0, "isGenerated": true, "path": "build/Desktop_Qt_5_15_0_MinGW_64_bit-Debug/TempDetectQt_autogen/mocs_compilation.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "main.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "mainwindow.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "modbusmanager.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "qcustomplot.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "temperaturedata.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "mainwindow.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "qcustomplot.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "modbusmanager.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "temperaturedata.h", "sourceGroupIndex": 1}, {"backtrace": 1, "isGenerated": true, "path": "build/Desktop_Qt_5_15_0_MinGW_64_bit-Debug/ui_mainwindow.h", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_5_15_0_MinGW_64_bit-Debug/TempDetectQt_autogen/timestamp", "sourceGroupIndex": 2}, {"backtrace": 0, "path": "mainwindow.ui", "sourceGroupIndex": 3}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_5_15_0_MinGW_64_bit-Debug/TempDetectQt_autogen/timestamp.rule", "sourceGroupIndex": 4}], "type": "EXECUTABLE"}